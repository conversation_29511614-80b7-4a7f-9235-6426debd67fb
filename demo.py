#!/usr/bin/env python3
"""
DBLP Author Information Query Demo

A concise demo program showing how to use DBLP API to search authors and get detailed profile information.

Usage:
    python demo.py
"""

import warnings
import dblp
from dblp.parsers import DBLPParseWarning

# Suppress DBLP parsing warnings
warnings.filterwarnings('ignore', category=DBLPParseWarning)


def print_header(title: str, char: str = "=") -> None:
    """Print formatted header"""
    print(f"\n{char * 60}")
    print(f" {title}")
    print(f"{char * 60}")


def print_separator(char: str = "-") -> None:
    """Print separator line"""
    print(char * 60)


def search_authors(query: str):
    """Search for authors"""
    print(f"\n🔍 Searching for author: '{query}'...")

    # Use project's DBLP API to search authors
    results = dblp.search_author(query)

    # Filter out None results
    valid_results = [r for r in results if r is not None]

    if not valid_results:
        print("❌ No matching authors found")
        return None

    # Display search results
    print_header("Search Results")
    for i, result in enumerate(valid_results, 1):
        author_name = result.get('author', 'Unknown Author')
        url = result.get('url', '')
        print(f"{i}. {author_name}")
        if url:
            print(f"   URL: {url}")
        print()

    # Auto-select if only one result
    if len(valid_results) == 1:
        print("✅ Found unique matching author, auto-selecting")
        return valid_results[0]

    # Let user choose
    while True:
        choice = input(f"\n📝 Please select author (1-{len(valid_results)}): ").strip()
        if choice.isdigit():
            choice_num = int(choice)
            if 1 <= choice_num <= len(valid_results):
                return valid_results[choice_num - 1]
        print(f"⚠️  Please enter a number between 1 and {len(valid_results)}")


def fetch_author_profile(author_info):
    """Fetch author detailed information"""
    url = author_info.get('url', '')
    author_name = author_info.get('author', 'Unknown Author')

    print(f"\n📊 Fetching detailed information for {author_name}...")
    return dblp.fetch_author_profile(url)


def display_author_profile(profile):
    """Display author detailed information"""
    print_header(f"Author Profile: {profile.name}", "=")

    # Basic information
    print(f"👤 Name: {profile.name}")
    print(f"🆔 DBLP PID: {profile.pid}")
    print(f"📚 Total Publications: {profile.publication_count}")
    print(f"📊 Actual Publications Found: {len(profile.publications)}")

    # Statistics
    stats = profile.get_publication_stats()
    print_separator()
    print("📈 Publication Statistics:")
    print(f"   Journal Articles: {stats['journal_articles']}")
    print(f"   Conference Papers: {stats['conference_papers']}")
    print(f"   Active Years: {stats['active_years']} years")
    if stats['first_publication_year'] and stats['last_publication_year']:
        print(f"   Publication Span: {stats['first_publication_year']} - {stats['last_publication_year']}")
    print(f"   Coauthors: {stats['coauthors_count']}")
    print(f"   Awards: {stats['awards_count']}")

    # Affiliation information
    if profile.affiliations:
        print_separator()
        print(f"🏛️  Affiliations ({len(profile.affiliations)}):")
        for i, affiliation in enumerate(profile.affiliations[:5], 1):
            print(f"   {i}. {affiliation}")
        if len(profile.affiliations) > 5:
            print(f"   ... and {len(profile.affiliations) - 5} more affiliations")

    # Awards information
    if profile.awards:
        print_separator()
        print(f"🏆 Awards ({len(profile.awards)}):")
        for i, award in enumerate(profile.awards[:5], 1):
            year = award.get('year', 'Unknown')
            award_name = award.get('award', 'Unknown Award')
            print(f"   {i}. {year}: {award_name}")
        if len(profile.awards) > 5:
            print(f"   ... and {len(profile.awards) - 5} more awards")

    # Recent publications
    recent_pubs = []
    for year in range(2020, 2026):  # 2020-2025
        year_pubs = profile.get_publications_by_year(year)
        recent_pubs.extend(year_pubs)

    if recent_pubs:
        print_separator()
        print(f"📝 Recent Publications (2020-2025): {len(recent_pubs)} papers")
        for i, pub in enumerate(recent_pubs[:5], 1):
            pub_type = "Journal" if hasattr(pub, 'journal') else "Conference"
            venue = getattr(pub, 'journal', getattr(pub, 'booktitle', 'Unknown'))
            print(f"   {i}. [{pub_type}] {pub.title}")
            print(f"      {venue}, {pub.year}")
        if len(recent_pubs) > 5:
            print(f"   ... and {len(recent_pubs) - 5} more recent publications")

    # Main collaborators
    coauthors = profile.get_coauthors()
    if coauthors:
        print_separator()
        print(f"🤝 Main Collaborators (showing top 10):")
        for i, coauthor in enumerate(coauthors[:10], 1):
            print(f"   {i}. {coauthor}")
        if len(coauthors) > 10:
            print(f"   ... and {len(coauthors) - 10} more collaborators")

    # Personal homepage links
    if profile.urls:
        print_separator()
        print(f"🔗 Related Links ({len(profile.urls)}):")
        for i, url in enumerate(profile.urls[:3], 1):
            print(f"   {i}. {url}")
        if len(profile.urls) > 3:
            print(f"   ... and {len(profile.urls) - 3} more links")


def main():
    """Main function"""
    print_header("DBLP Author Information Query Demo")
    print("Welcome to DBLP Author Information Query System!")

    # Get user input
    query = input("\n🔍 Please enter author search keywords: ").strip()

    # Search for authors
    selected_author = search_authors(query)
    if not selected_author:
        return

    # Fetch and display author detailed information
    profile = fetch_author_profile(selected_author)
    display_author_profile(profile)


if __name__ == "__main__":
    main()
