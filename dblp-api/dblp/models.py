"""
Data models for DBLP API.

This module contains all the data classes used to represent DBLP entities
such as authors, publications, and author profiles.
"""

from dataclasses import dataclass
from typing import List, Optional


@dataclass
class Author:
    """Represents an author with name and optional DBLP person ID."""
    name: str
    pid: Optional[str] = None

    def __str__(self) -> str:
        if self.pid:
            return f"{self.name} (pid: {self.pid})"
        return self.name


@dataclass
class Publication:
    """Base class for publications."""
    key: str
    title: str
    authors: List[Author]
    year: int
    urls: List[str]
    mdate: Optional[str] = None
    rank: Optional[str] = None  # CCF class (A, B, C)
    category: Optional[str] = None  # CCF category number

    def __str__(self) -> str:
        author_names = [author.name for author in self.authors]
        if len(author_names) > 3:
            author_str = ", ".join(author_names[:3]) + " et al."
        else:
            author_str = ", ".join(author_names)
        return f"{self.title} ({author_str}, {self.year})"

    @property
    def author_names(self) -> List[str]:
        """Get list of author names."""
        return [author.name for author in self.authors]

    def set_ccf_attrs(self, venue: Optional[str] = None) -> None:
        """
        Automatically set CCF rank and category based on venue information.

        Args:
            venue: Venue name to look up. If None, will try to extract from publication.
        """
        # Import here to avoid circular imports
        from dblp.api import get_ccf_line

        # Determine venue to look up
        lookup_venue = venue
        if lookup_venue is None:
            # Try to extract venue from publication type
            if hasattr(self, 'journal') and self.journal:
                lookup_venue = self.journal
            elif hasattr(self, 'booktitle') and self.booktitle:
                lookup_venue = self.booktitle

        # Get CCF information
        ccf_info = get_ccf_line(lookup_venue)
        if ccf_info:
            self.rank = ccf_info.get('class')
            self.category = ccf_info.get('category')


@dataclass
class Article(Publication):
    """Represents a journal article."""
    journal: str = ""
    volume: Optional[str] = None
    number: Optional[str] = None
    pages: Optional[str] = None
    publtype: Optional[str] = None

    def __str__(self) -> str:
        base_str = super().__str__()
        venue_info = self.journal
        if self.volume:
            venue_info += f" {self.volume}"
            if self.number:
                venue_info += f"({self.number})"
        if self.pages:
            venue_info += f": {self.pages}"
        return f"[Journal] {base_str} - {venue_info}"

    def set_ccf_attrs_auto(self) -> None:
        """Automatically set CCF rank and category based on journal name."""
        self.set_ccf_attrs(self.journal)


@dataclass
class InProceedings(Publication):
    """Represents a conference paper."""
    booktitle: str = ""
    pages: Optional[str] = None
    crossref: Optional[str] = None

    def __str__(self) -> str:
        base_str = super().__str__()
        venue_info = self.booktitle
        if self.pages:
            venue_info += f": {self.pages}"
        return f"[Conference] {base_str} - {venue_info}"

    def set_ccf_attrs_auto(self) -> None:
        """Automatically set CCF rank and category based on conference name."""
        self.set_ccf_attrs(self.booktitle)


@dataclass
class AuthorProfile:
    """Represents a complete author profile from DBLP."""
    name: str
    pid: str
    urls: List[str]
    affiliations: List[str]
    awards: List[dict]
    publications: List[Publication]
    publication_count: int

    def __str__(self) -> str:
        return f"AuthorProfile(name='{self.name}', pid='{self.pid}', publications={len(self.publications)})"

    def get_articles(self) -> List[Article]:
        """Get all journal articles."""
        return [pub for pub in self.publications if isinstance(pub, Article)]

    def get_conference_papers(self) -> List[InProceedings]:
        """Get all conference papers."""
        return [pub for pub in self.publications if isinstance(pub, InProceedings)]

    def get_publications_by_year(self, year: int) -> List[Publication]:
        """Get all publications from a specific year."""
        return [pub for pub in self.publications if pub.year == year]

    def get_publications_by_venue(self, venue: str) -> List[Publication]:
        """Get all publications from a specific venue (journal or conference)."""
        venue_lower = venue.lower()
        result = []
        for pub in self.publications:
            if isinstance(pub, Article) and venue_lower in pub.journal.lower():
                result.append(pub)
            elif isinstance(pub, InProceedings) and venue_lower in pub.booktitle.lower():
                result.append(pub)
        return result

    def get_coauthors(self) -> List[str]:
        """Get list of unique coauthor names (excluding the profile owner)."""
        coauthors = set()
        for pub in self.publications:
            for author in pub.authors:
                if author.name != self.name:
                    coauthors.add(author.name)
        return sorted(list(coauthors))

    def get_publication_years(self) -> List[int]:
        """Get sorted list of years when publications were made."""
        years = sorted(set(pub.year for pub in self.publications if pub.year > 0))
        return years

    def get_publication_stats(self) -> dict:
        """Get publication statistics."""
        articles = self.get_articles()
        conference_papers = self.get_conference_papers()
        years = self.get_publication_years()
        
        return {
            'total_publications': len(self.publications),
            'journal_articles': len(articles),
            'conference_papers': len(conference_papers),
            'first_publication_year': years[0] if years else None,
            'last_publication_year': years[-1] if years else None,
            'active_years': len(years),
            'coauthors_count': len(self.get_coauthors()),
            'awards_count': len(self.awards),
            'affiliations_count': len(self.affiliations)
        }
