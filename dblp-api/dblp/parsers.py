"""
XML parsers for DBLP data.

This module contains functions to parse DBLP XML responses and convert them
into structured data models. It includes warning mechanisms for unexpected
XML formats.
"""

import warnings
import xml.etree.ElementTree as ET
from typing import List, Optional, Set

from .models import Author, Publication, Article, InProceedings, AuthorProfile


class DBLPParseWarning(UserWarning):
    """Custom warning class for DBLP parsing issues."""
    pass


def _warn_unexpected_element(element_name: str, parent_context: str, element_text: str = None):
    """Issue a warning for unexpected XML elements."""
    msg = f"Unexpected element '{element_name}' in {parent_context}"
    if element_text:
        msg += f" with content: '{element_text[:100]}...'" if len(element_text) > 100 else f" with content: '{element_text}'"
    warnings.warn(msg, DBLPParseWarning, stacklevel=3)


def parse_author_from_xml(author_elem) -> Author:
    """
    Parse an author element from XML.
    
    Args:
        author_elem: XML element representing an author
        
    Returns:
        Author object
    """
    if author_elem is None:
        warnings.warn("Received None author element", DBLPParseWarning)
        return Author(name="Unknown", pid=None)
    
    name = author_elem.text or ""
    pid = author_elem.get('pid')
    
    # Check for unexpected attributes
    expected_attrs = {'pid'}
    actual_attrs = set(author_elem.attrib.keys())
    unexpected_attrs = actual_attrs - expected_attrs
    
    if unexpected_attrs:
        warnings.warn(
            f"Unexpected attributes in author element: {unexpected_attrs}",
            DBLPParseWarning
        )
    
    return Author(name=name, pid=pid)


def parse_publication_from_xml(r_elem) -> Optional[Publication]:
    """
    Parse a publication from an <r> element.
    
    Args:
        r_elem: XML <r> element containing publication data
        
    Returns:
        Publication object or None if parsing fails
    """
    if r_elem is None:
        return None
    
    # Get the publication element (article or inproceedings)
    pub_elem = None
    pub_type = None
    
    for child in r_elem:
        if child.tag in ['article', 'inproceedings']:
            if pub_elem is not None:
                warnings.warn(
                    f"Multiple publication elements found in <r>: {child.tag} and {pub_type}",
                    DBLPParseWarning
                )
            pub_elem = child
            pub_type = child.tag
        elif child.tag not in ['article', 'inproceedings']:
            _warn_unexpected_element(child.tag, "<r> element", child.text)
    
    if pub_elem is None:
        warnings.warn("No publication element found in <r>", DBLPParseWarning)
        return None
    
    # Extract common fields
    key = pub_elem.get('key', '')
    mdate = pub_elem.get('mdate')
    title = ''
    authors = []
    year = 0
    urls = []
    
    # Track processed elements to detect unexpected ones
    processed_elements = set()
    
    for elem in pub_elem:
        processed_elements.add(elem.tag)
        
        if elem.tag == 'title':
            title = elem.text or ''
        elif elem.tag == 'author':
            authors.append(parse_author_from_xml(elem))
        elif elem.tag == 'year':
            try:
                year = int(elem.text or 0)
            except ValueError:
                warnings.warn(
                    f"Invalid year format: '{elem.text}' in publication {key}",
                    DBLPParseWarning
                )
                year = 0
        elif elem.tag == 'ee':
            if elem.text:
                urls.append(elem.text)
        elif elem.tag == 'url':
            if elem.text:
                urls.append(f"https://dblp.org/{elem.text}")
    
    # Parse type-specific fields
    if pub_type == 'article':
        return _parse_article(pub_elem, key, title, authors, year, urls, mdate, processed_elements)
    elif pub_type == 'inproceedings':
        return _parse_inproceedings(pub_elem, key, title, authors, year, urls, mdate, processed_elements)
    
    return None


def _parse_article(pub_elem, key: str, title: str, authors: List[Author], 
                  year: int, urls: List[str], mdate: Optional[str], 
                  processed_elements: Set[str]) -> Article:
    """Parse article-specific fields."""
    journal = ''
    volume = None
    number = None
    pages = None
    publtype = pub_elem.get('publtype')
    
    # Expected elements for articles
    expected_elements = {
        'title', 'author', 'year', 'ee', 'url', 'journal', 
        'volume', 'number', 'pages', 'doi', 'crossref'
    }
    
    for elem in pub_elem:
        if elem.tag == 'journal':
            journal = elem.text or ''
        elif elem.tag == 'volume':
            volume = elem.text
        elif elem.tag == 'number':
            number = elem.text
        elif elem.tag == 'pages':
            pages = elem.text
        elif elem.tag not in expected_elements:
            _warn_unexpected_element(elem.tag, f"article {key}", elem.text)
    
    return Article(
        key=key, title=title, authors=authors, year=year, urls=urls,
        mdate=mdate, journal=journal, volume=volume, number=number,
        pages=pages, publtype=publtype
    )


def _parse_inproceedings(pub_elem, key: str, title: str, authors: List[Author], 
                        year: int, urls: List[str], mdate: Optional[str], 
                        processed_elements: Set[str]) -> InProceedings:
    """Parse inproceedings-specific fields."""
    booktitle = ''
    pages = None
    crossref = None
    
    # Expected elements for inproceedings
    expected_elements = {
        'title', 'author', 'year', 'ee', 'url', 'booktitle', 
        'pages', 'crossref', 'doi'
    }
    
    for elem in pub_elem:
        if elem.tag == 'booktitle':
            booktitle = elem.text or ''
        elif elem.tag == 'pages':
            pages = elem.text
        elif elem.tag == 'crossref':
            crossref = elem.text
        elif elem.tag not in expected_elements:
            _warn_unexpected_element(elem.tag, f"inproceedings {key}", elem.text)
    
    return InProceedings(
        key=key, title=title, authors=authors, year=year, urls=urls,
        mdate=mdate, booktitle=booktitle, pages=pages, crossref=crossref
    )


def parse_author_profile_from_xml(xml_content: str) -> AuthorProfile:
    """
    Parse complete author profile from DBLP XML.
    
    Args:
        xml_content: Raw XML content as string
        
    Returns:
        AuthorProfile object
        
    Raises:
        ET.ParseError: If XML parsing fails
        ValueError: If required data is missing
    """
    try:
        root = ET.fromstring(xml_content)
    except ET.ParseError as e:
        raise ET.ParseError(f"Failed to parse XML: {e}")
    
    # Validate root element
    if root.tag != 'dblpperson':
        warnings.warn(
            f"Unexpected root element: '{root.tag}', expected 'dblpperson'",
            DBLPParseWarning
        )
    
    # Extract basic information from root element
    name = root.get('name', '')
    pid = root.get('pid', '')
    publication_count = 0
    
    try:
        publication_count = int(root.get('n', 0))
    except ValueError:
        warnings.warn(
            f"Invalid publication count: '{root.get('n')}'",
            DBLPParseWarning
        )
    
    # Initialize lists
    urls = []
    affiliations = []
    awards = []
    publications = []
    
    # Parse person element
    person_elem = root.find('person')
    if person_elem is not None:
        _parse_person_element(person_elem, urls, affiliations, awards)
    else:
        warnings.warn("No <person> element found in author profile", DBLPParseWarning)
    
    # Parse publications
    publication_elements = root.findall('r')
    for r_elem in publication_elements:
        pub = parse_publication_from_xml(r_elem)
        if pub:
            publications.append(pub)
    
    # Validate publication count
    if len(publications) != publication_count and publication_count > 0:
        warnings.warn(
            f"Publication count mismatch: found {len(publications)}, expected {publication_count}",
            DBLPParseWarning
        )
    
    return AuthorProfile(
        name=name,
        pid=pid,
        urls=urls,
        affiliations=affiliations,
        awards=awards,
        publications=publications,
        publication_count=max(publication_count, len(publications))
    )


def _parse_person_element(person_elem, urls: List[str], affiliations: List[str], awards: List[dict]):
    """Parse the <person> element for URLs, affiliations, and awards."""
    expected_elements = {'author', 'url', 'note'}
    
    for elem in person_elem:
        if elem.tag == 'url' and elem.text:
            urls.append(elem.text)
        elif elem.tag == 'note':
            note_type = elem.get('type')
            if note_type == 'affiliation' and elem.text:
                affiliations.append(elem.text)
            elif note_type == 'award' and elem.text:
                label = elem.get('label', '')
                awards.append({
                    'year': label,
                    'award': elem.text
                })
            elif note_type not in ['affiliation', 'award']:
                _warn_unexpected_element(f"note[type='{note_type}']", "person element", elem.text)
        elif elem.tag == 'author':
            # Author element in person is expected, just skip
            pass
        elif elem.tag not in expected_elements:
            _warn_unexpected_element(elem.tag, "person element", elem.text)
