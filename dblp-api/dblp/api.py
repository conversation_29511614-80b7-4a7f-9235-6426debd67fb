import requests
import pandas as pd

from urllib.parse import urlencode
from importlib.resources import open_binary

from .models import AuthorProfile
from .parsers import parse_author_profile_from_xml

BASE_URL = 'https://dblp.org/search/%s/api'


def add_ccf_class(results: list[dict]) -> list[dict]:
    def get_ccf_class(venue: str | None, catalog: pd.DataFrame) -> str | None:
        if venue is None:
            return None
        if len(series := catalog.loc[catalog.get('abbr').str.lower() == venue.lower(), 'class']) > 0:
            return series.item()
        if len(series := catalog.loc[catalog.get('url').str.contains(f'/{venue.lower()}/'), 'class']) > 0:
            return series.item()
        return None

    catalog = pd.read_csv(open_binary('dblp.data', 'ccf_catalog.csv'))
    for result in results:
        result['ccf_class'] = get_ccf_class(result.get('venue'), catalog=catalog)
    return results

_catalog = None
def _get_catalog() -> pd.DataFrame:
    global _catalog
    if _catalog is None:
        _catalog = pd.read_csv(open_binary('dblp.data', 'ccf_catalog.csv'))
    return _catalog

def get_ccf_line(venue: str | None) -> dict[str, str] | None:
    if venue is None:
        return None
    catalog = _get_catalog()
    if len(series := catalog.loc[catalog.get('abbr').str.lower() == venue.lower()]) > 0:
        return series.iloc[0].to_dict()
    if len(series := catalog.loc[catalog.get('url').str.contains(f'/{venue.lower()}/')]) > 0:
        return series.iloc[0].to_dict()
    return None

def add_ccf_attrs(results: list[dict], attrs: list[str] | str) -> list[dict]:
    if isinstance(attrs, str):
        attrs = [attrs]
    for result in results:
        line = get_ccf_line(result.get('venue'))
        if line is not None:
            for attr in attrs:
                result[attr] = line.get(attr)
    return results

def search_pub(queries: list[str]) -> list[dict | None]:
    api = BASE_URL % 'publ'

    if isinstance(queries, str):
        queries = [queries]
    results = []
    for query in queries:
        options = {
            'q': query,
            'format': 'json',
            'h': 1,
        }
        r = requests.get(f'{api}?{urlencode(options)}').json()
        hit = r['result']['hits'].get('hit')
        if hit is not None:
            info = hit[0].get('info')
            info['authors'] = [author['text'] for author in info['authors']['author']]
            results.append(info)
        else:
            results.append(None)
    return results

def search_author(queries: list[str] | str) -> list[dict | None]:
    api = BASE_URL % 'author'

    if isinstance(queries, str):
        queries = [queries]
    results = []
    for query in queries:
        options = {
            'q': query,
            'format': 'json',
            'h': 1,
        }
        r = requests.get(f'{api}?{urlencode(options)}').json()
        hit = r['result']['hits'].get('hit')
        if hit is not None:
            info = hit[0].get('info')
            results.append(info)
        else:
            results.append(None)
    return results



def fetch_author_profile(url: str) -> AuthorProfile:
    """
    Fetch author profile from DBLP.

    Args:
        url: DBLP author page URL (e.g., https://dblp.org/pid/10/3248)

    Returns:
        AuthorProfile object containing author information and publications

    Raises:
        requests.RequestException: If HTTP request fails
        ValueError: If URL format is invalid or required data is missing
    """
    # Convert URL to XML API endpoint
    if not url.startswith('https://dblp.org/pid/'):
        raise ValueError(f"Invalid DBLP URL format: {url}")

    xml_url = url + '.xml'

    try:
        # Fetch XML data
        response = requests.get(xml_url, timeout=30)
        response.raise_for_status()

        # Parse XML using the dedicated parser
        return parse_author_profile_from_xml(response.text)

    except requests.RequestException as e:
        raise requests.RequestException(f"Failed to fetch data from {xml_url}: {e}")
    except Exception as e:
        raise ValueError(f"Error processing author profile: {e}")