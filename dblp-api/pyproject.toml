[build-system]
requires = ["setuptools>=68.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "dblp-api"
version = "0.3.0"
description = "A helper package to get information of scholarly articles from DBLP using its public API."
readme = "README.md"
keywords = ["dblp", "api", "scholarly-articles"]
license = {file = "LICENSE"}
authors = [{name = "alumik"}]
requires-python = ">=3.9"

classifiers = [
    "Programming Language :: Python",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3 :: Only",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]

dependencies = [
    "fire",
    "pandas",
    "requests",
]

[tool.setuptools.packages.find]
where = ["."]
include = ["dblp*"]
exclude = ["samples*"]
namespaces = false
