#!/usr/bin/env python3
"""
Usage example for the DBLP Author Profile API.

This script demonstrates how to use the fetch_author_profile function
and work with the returned data structures.
"""

import sys
import os
import warnings

# Add the dblp-api directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'dblp-api'))

import dblp
from dblp.parsers import DBLPParseWarning

def main():
    """Main example function."""
    
    # Configure warnings - you can filter them if needed
    # warnings.filterwarnings('ignore', category=DBLPParseWarning)  # To suppress warnings
    warnings.filterwarnings('always', category=DBLPParseWarning)    # To show all warnings
    
    # Example 1: Fetch <PERSON>'s profile
    print("=== Example 1: Fetching <PERSON>'s Profile ===")
    
    try:
        url = "https://dblp.org/pid/10/3248"
        profile = dblp.fetch_author_profile(url)
        
        print(f"Author: {profile.name}")
        print(f"DBLP PID: {profile.pid}")
        print(f"Total Publications: {len(profile.publications)}")
        print(f"Affiliations: {', '.join(profile.affiliations)}")
        
        # Get publication statistics
        stats = profile.get_publication_stats()
        print(f"\nPublication Statistics:")
        print(f"  Journal Articles: {stats['journal_articles']}")
        print(f"  Conference Papers: {stats['conference_papers']}")
        print(f"  Active Years: {stats['active_years']} ({stats['first_publication_year']}-{stats['last_publication_year']})")
        print(f"  Coauthors: {stats['coauthors_count']}")
        print(f"  Awards: {stats['awards_count']}")
        
        # Show recent publications
        recent_pubs = profile.get_publications_by_year(2023)
        print(f"\nPublications in 2023 ({len(recent_pubs)}):")
        for i, pub in enumerate(recent_pubs[:3], 1):  # Show first 3
            print(f"  {i}. {pub.title}")
            venue = pub.journal if hasattr(pub, 'journal') else pub.booktitle
            print(f"     {venue}")
        
        # Show publications from a specific venue
        nature_pubs = profile.get_publications_by_venue("Nature")
        print(f"\nPublications in Nature journals ({len(nature_pubs)}):")
        for pub in nature_pubs[:2]:  # Show first 2
            print(f"  - {pub.title} ({pub.year})")
        
        # Show some coauthors
        coauthors = profile.get_coauthors()
        print(f"\nSome frequent coauthors:")
        for author in coauthors[:5]:  # Show first 5
            print(f"  - {author}")
        
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n" + "="*60)
    
    # Example 2: Working with individual publications
    print("=== Example 2: Working with Publications ===")
    
    if 'profile' in locals():
        # Get the most recent publication
        recent_pub = max(profile.publications, key=lambda p: p.year)
        print(f"Most recent publication ({recent_pub.year}):")
        print(f"  Title: {recent_pub.title}")
        print(f"  Authors: <AUTHORS>
        
        if hasattr(recent_pub, 'journal'):
            print(f"  Journal: {recent_pub.journal}")
        elif hasattr(recent_pub, 'booktitle'):
            print(f"  Conference: {recent_pub.booktitle}")
        
        print(f"  URLs: {len(recent_pub.urls)} available")
        
        # Show publication by type
        articles = profile.get_articles()
        conferences = profile.get_conference_papers()
        
        print(f"\nPublication breakdown:")
        print(f"  Journal articles: {len(articles)}")
        print(f"  Conference papers: {len(conferences)}")
        
        # Show a sample article
        if articles:
            sample_article = articles[0]
            print(f"\nSample journal article:")
            print(f"  {sample_article}")
        
        # Show a sample conference paper
        if conferences:
            sample_conf = conferences[0]
            print(f"\nSample conference paper:")
            print(f"  {sample_conf}")

def example_error_handling():
    """Example of error handling."""
    print("\n=== Example 3: Error Handling ===")
    
    invalid_urls = [
        "https://dblp.org/pid/999/999",  # Non-existent author
        "https://invalid-url.com",       # Invalid URL format
    ]
    
    for url in invalid_urls:
        try:
            print(f"Trying to fetch: {url}")
            profile = dblp.fetch_author_profile(url)
            print(f"Success: {profile.name}")
        except ValueError as e:
            print(f"ValueError: {e}")
        except Exception as e:
            print(f"Other error: {type(e).__name__}: {e}")
        print()

if __name__ == "__main__":
    print("🔬 DBLP Author Profile API Usage Examples")
    print("=" * 60)
    
    main()
    example_error_handling()
    
    print("\n✅ Examples completed!")
    print("\nTip: You can suppress parsing warnings by using:")
    print("  warnings.filterwarnings('ignore', category=DBLPParseWarning)")
