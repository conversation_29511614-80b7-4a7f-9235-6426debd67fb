#!/usr/bin/env python3
"""
Test script for fetch_author_profile function.
Tests the implementation with <PERSON>'s DBLP profile.
"""

import sys
import os
import warnings

# Add the dblp-api directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'dblp-api'))

import dblp
from dblp import AuthorProfile, Article, InProceedings
from dblp.parsers import DBLPParseWarning

# Configure warnings to show DBLP parsing warnings
warnings.filterwarnings('always', category=DBLPParseWarning)

def test_fetch_author_profile():
    """Test fetching <PERSON>'s profile."""
    url = "https://dblp.org/pid/10/3248"
    
    print(f"Fetching author profile from: {url}")
    
    try:
        profile = dblp.fetch_author_profile(url)
        
        print(f"\n=== Author Profile ===")
        print(f"Name: {profile.name}")
        print(f"PID: {profile.pid}")
        print(f"Publication Count: {profile.publication_count}")
        print(f"Actual Publications Found: {len(profile.publications)}")
        
        print(f"\n=== URLs ({len(profile.urls)}) ===")
        for i, url in enumerate(profile.urls[:5], 1):  # Show first 5 URLs
            print(f"{i}. {url}")
        if len(profile.urls) > 5:
            print(f"... and {len(profile.urls) - 5} more")
        
        print(f"\n=== Affiliations ({len(profile.affiliations)}) ===")
        for i, affiliation in enumerate(profile.affiliations, 1):
            print(f"{i}. {affiliation}")
        
        print(f"\n=== Awards ({len(profile.awards)}) ===")
        for i, award in enumerate(profile.awards, 1):
            print(f"{i}. {award['year']}: {award['award']}")
        
        # Analyze publications by type
        articles = profile.get_articles()
        conference_papers = profile.get_conference_papers()
        
        print(f"\n=== Publication Statistics ===")
        print(f"Total Publications: {len(profile.publications)}")
        print(f"Journal Articles: {len(articles)}")
        print(f"Conference Papers: {len(conference_papers)}")
        
        # Show recent publications (last 5 years)
        recent_years = [2020, 2021, 2022, 2023, 2024, 2025]
        recent_pubs = []
        for year in recent_years:
            year_pubs = profile.get_publications_by_year(year)
            recent_pubs.extend(year_pubs)
        
        print(f"\n=== Recent Publications (2020-2025): {len(recent_pubs)} ===")
        for i, pub in enumerate(recent_pubs[:10], 1):  # Show first 10
            pub_type = "Journal" if isinstance(pub, Article) else "Conference"
            venue = pub.journal if isinstance(pub, Article) else pub.booktitle
            print(f"{i}. [{pub_type}] {pub.title}")
            print(f"   {venue}, {pub.year}")
            print(f"   Authors: <AUTHORS>
            print()
        
        if len(recent_pubs) > 10:
            print(f"... and {len(recent_pubs) - 10} more recent publications")
        
        print("✅ Test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_url_conversion():
    """Test URL conversion logic."""
    test_urls = [
        "https://dblp.org/pid/10/3248",
        "https://dblp.org/pid/123/456",
    ]
    
    print("\n=== Testing URL Conversion ===")
    for url in test_urls:
        expected = url + ".xml"
        print(f"Input: {url}")
        print(f"Expected XML URL: {expected}")
        print()

def test_error_handling():
    """Test error handling with invalid URLs."""
    print("\n=== Testing Error Handling ===")
    
    invalid_urls = [
        "https://invalid-url.com",
        "https://dblp.org/invalid/path",
        "not-a-url",
    ]
    
    for url in invalid_urls:
        print(f"Testing invalid URL: {url}")
        try:
            profile = dblp.fetch_author_profile(url)
            print(f"❌ Expected error but got result: {profile.name}")
        except Exception as e:
            print(f"✅ Correctly caught error: {type(e).__name__}: {e}")
        print()

if __name__ == "__main__":
    print("🧪 Testing DBLP Author Profile Fetcher")
    print("=" * 50)
    
    # Test URL conversion
    test_url_conversion()
    
    # Test main functionality
    success = test_fetch_author_profile()
    
    # Test error handling
    test_error_handling()
    
    if success:
        print("\n🎉 All tests completed!")
    else:
        print("\n💥 Some tests failed!")
        sys.exit(1)
