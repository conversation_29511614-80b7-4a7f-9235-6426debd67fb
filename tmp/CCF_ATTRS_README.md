# CCF 属性自动设置功能

基于 `dblp.add_ccf_attrs` 函数，为 `Publication`、`Article` 和 `InProceedings` 类添加了自动设置 CCF 相关信息的能力。

## 新增功能

### 1. Publication 基类新增属性

- `rank: Optional[str]` - CCF 等级 (A, B, C)
- `category: Optional[str]` - CCF 类别编号

### 2. 新增方法

#### Publication.set_ccf_attrs(venue: Optional[str] = None)
手动设置 CCF 属性。

**参数:**
- `venue`: 期刊或会议名称。如果为 None，会自动从 publication 中提取

**示例:**
```python
article.set_ccf_attrs("TOCS")  # 手动指定期刊名称
article.set_ccf_attrs()        # 自动从 article.journal 提取
```

#### Article.set_ccf_attrs_auto()
自动基于期刊名称设置 CCF 属性。

**示例:**
```python
article = Article(journal="TOCS", ...)
article.set_ccf_attrs_auto()
print(f"Rank: {article.rank}, Category: {article.category}")
# 输出: Rank: A, Category: 1
```

#### InProceedings.set_ccf_attrs_auto()
自动基于会议名称设置 CCF 属性。

**示例:**
```python
inproc = InProceedings(booktitle="SIGMOD", ...)
inproc.set_ccf_attrs_auto()
print(f"Rank: {inproc.rank}, Category: {inproc.category}")
# 输出: Rank: A, Category: 5
```

### 3. 改进的 get_ccf_line 函数

现在支持多种匹配方式：
1. 期刊/会议缩写精确匹配
2. 完整名称精确匹配
3. 完整名称部分匹配
4. URL 模式匹配

**示例:**
```python
# 以下都能找到 TOCS 的信息
dblp.get_ccf_line("TOCS")                              # 缩写
dblp.get_ccf_line("ACM Transactions on Computer Systems")  # 完整名称
dblp.get_ccf_line("Transactions on Computer Systems")      # 部分名称
```

### 4. 自动解析集成

在解析 XML 创建 `Article` 和 `InProceedings` 对象时，会自动调用 `set_ccf_attrs_auto()` 设置 CCF 属性。

## 使用示例

### 基本用法

```python
import dblp
from dblp.models import Article, InProceedings, Author

# 创建期刊文章
authors = [Author("Alice"), Author("Bob")]
article = Article(
    key="journals/tocs/test2023",
    title="Test Article",
    authors=authors,
    year=2023,
    urls=["https://example.com"],
    journal="ACM Transactions on Computer Systems"
)

# 自动设置 CCF 属性
article.set_ccf_attrs_auto()
print(f"CCF Rank: {article.rank}")      # A
print(f"CCF Category: {article.category}")  # 1

# 创建会议论文
inproc = InProceedings(
    key="conf/sigmod/test2023",
    title="Test Conference Paper",
    authors=authors,
    year=2023,
    urls=["https://example.com"],
    booktitle="SIGMOD"
)

# 自动设置 CCF 属性
inproc.set_ccf_attrs_auto()
print(f"CCF Rank: {inproc.rank}")      # A
print(f"CCF Category: {inproc.category}")  # 5
```

### 与搜索结果结合使用

```python
# 搜索论文
results = dblp.search(["MapReduce: Simplified Data Processing"])

# 为搜索结果添加 CCF 属性
results_with_ccf = dblp.add_ccf_attrs(results, ['class', 'category'])

for result in results_with_ccf:
    print(f"Title: {result['title']}")
    print(f"CCF Class: {result.get('class', 'Not found')}")
    print(f"CCF Category: {result.get('category', 'Not found')}")
```

### 手动设置

```python
# 当自动匹配失败时，可以手动指定
article = Article(journal="ACM Trans. Comput. Syst.", ...)
article.set_ccf_attrs_auto()  # 可能失败，因为缩写形式

if article.rank is None:
    article.set_ccf_attrs("TOCS")  # 手动指定标准缩写
```

## CCF 类别说明

- **Rank (等级)**: A, B, C
- **Category (类别)**: 数字编号，表示不同的研究领域
  - 1: 计算机系统与高性能计算
  - 4: 软件工程/系统软件/程序设计语言
  - 5: 数据库/数据挖掘/内容检索
  - 8: 人工智能
  - 等等...

## 注意事项

1. CCF 属性设置基于 CCF 目录数据，如果期刊/会议不在目录中，属性将保持 `None`
2. 匹配优先级：缩写 > 完整名称 > 部分名称 > URL 模式
3. 自动解析时会在创建对象后立即设置 CCF 属性
4. 可以随时调用 `set_ccf_attrs()` 或 `set_ccf_attrs_auto()` 重新设置属性
