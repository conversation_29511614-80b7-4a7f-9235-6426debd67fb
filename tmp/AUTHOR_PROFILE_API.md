# DBLP Author Profile API

## 概述

`fetch_author_profile` 函数提供了从 DBLP 获取作者详细信息的功能。该实现采用模块化设计，具有完善的错误处理和警告机制。

## 模块结构

### 1. `dblp.models` - 数据模型
包含所有数据类定义：
- `Author`: 作者信息（姓名、PID）
- `Publication`: 发表论文基类
- `Article`: 期刊论文
- `InProceedings`: 会议论文  
- `AuthorProfile`: 完整的作者档案

### 2. `dblp.parsers` - XML解析器
包含XML解析逻辑和警告机制：
- `parse_author_profile_from_xml()`: 主解析函数
- `parse_publication_from_xml()`: 论文解析
- `DBLPParseWarning`: 自定义警告类

### 3. `dblp.api` - API接口
包含主要的API函数：
- `fetch_author_profile()`: 获取作者档案的主函数

## 基本使用

```python
import dblp

# 获取作者档案
url = "https://dblp.org/pid/10/3248"  # Geoffrey <PERSON>nton
profile = dblp.fetch_author_profile(url)

print(f"作者: {profile.name}")
print(f"论文数量: {len(profile.publications)}")
print(f"机构: {', '.join(profile.affiliations)}")
```

## 数据结构

### AuthorProfile
```python
@dataclass
class AuthorProfile:
    name: str                    # 作者姓名
    pid: str                     # DBLP person ID
    urls: List[str]             # 相关链接
    affiliations: List[str]      # 机构信息
    awards: List[dict]          # 奖项列表
    publications: List[Publication]  # 发表论文
    publication_count: int       # 论文总数
```

### 实用方法
```python
# 获取期刊论文
articles = profile.get_articles()

# 获取会议论文
conferences = profile.get_conference_papers()

# 按年份筛选
pubs_2023 = profile.get_publications_by_year(2023)

# 按期刊/会议筛选
nature_pubs = profile.get_publications_by_venue("Nature")

# 获取合作者
coauthors = profile.get_coauthors()

# 获取统计信息
stats = profile.get_publication_stats()
```

## 警告机制

当遇到意外的XML格式时，系统会发出警告：

```python
import warnings
from dblp.parsers import DBLPParseWarning

# 显示所有解析警告
warnings.filterwarnings('always', category=DBLPParseWarning)

# 或者忽略警告
warnings.filterwarnings('ignore', category=DBLPParseWarning)
```

### 常见警告类型

1. **意外的XML元素**: 
   ```
   Unexpected element 'month' in article journals/corr/abs-2501-17805
   ```

2. **意外的属性**:
   ```
   Unexpected attributes in author element: {'orcid'}
   ```

3. **缺失的发布类型**:
   ```
   No publication element found in <r>
   ```

4. **数据不匹配**:
   ```
   Publication count mismatch: found 289, expected 299
   ```

## 错误处理

```python
try:
    profile = dblp.fetch_author_profile(url)
except requests.RequestException as e:
    print(f"网络请求失败: {e}")
except ValueError as e:
    print(f"URL格式错误或数据问题: {e}")
except Exception as e:
    print(f"其他错误: {e}")
```

## 支持的发布类型

目前支持解析以下类型的发表论文：
- **期刊论文** (`article`): 包含期刊名、卷号、期号、页码等
- **会议论文** (`inproceedings`): 包含会议名、页码等

不支持的类型会触发警告，但不会中断解析过程：
- `incollection` (书籍章节)
- `phdthesis` (博士论文)
- `data` (数据集)

## 性能考虑

- 网络请求超时设置为30秒
- XML解析使用标准库 `xml.etree.ElementTree`
- 大型作者档案（如Geoffrey Hinton的299篇论文）解析时间约1-2秒

## 扩展性

模块化设计便于扩展：
- 在 `models.py` 中添加新的数据类
- 在 `parsers.py` 中添加新的解析逻辑
- 警告机制可以轻松适应新的XML格式

## 示例输出

```
=== Author Profile ===
Name: Geoffrey E. Hinton
PID: 10/3248
Publication Count: 299
Actual Publications Found: 289

=== Affiliations (2) ===
1. Google DeepMind, London, UK
2. University of Toronto, Department of Computer Science, ON, Canada

=== Awards (3) ===
1. 2018: Turing Award
2. 2016: BBVA Foundation Frontiers of Knowledge Award
3. 2024: Nobel Prize in Physics

=== Publication Statistics ===
Total Publications: 289
Journal Articles: 124
Conference Papers: 165
```

## 注意事项

1. **URL格式**: 必须是 `https://dblp.org/pid/` 开头的有效DBLP URL
2. **网络依赖**: 需要互联网连接访问DBLP API
3. **XML格式变化**: DBLP可能会更新XML格式，警告机制会提醒这些变化
4. **数据完整性**: 某些论文可能因为格式问题无法解析，但会在警告中报告
