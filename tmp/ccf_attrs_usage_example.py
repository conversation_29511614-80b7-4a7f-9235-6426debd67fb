#!/usr/bin/env python3
"""
Usage example for CCF attributes functionality in DBLP models.

This script demonstrates how to use the new CCF rank and category features
added to Publication, Article, and InProceedings classes.
"""

import sys
import os

# Add the dblp-api directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'dblp-api'))

import dblp
from dblp.models import Article, InProceedings, Author


def example_1_manual_creation_with_ccf():
    """Example 1: Manually create publications and set CCF attributes."""
    print("=== Example 1: Manual Creation with CCF Attributes ===")
    
    # Create authors
    authors = [
        Author("<PERSON>", "12/3456"),
        Author("<PERSON>", "78/9012"),
        Author("<PERSON>")
    ]
    
    # Create a journal article
    article = Article(
        key="journals/tocs/johnson2023",
        title="Advanced Database Systems: A Comprehensive Survey",
        authors=authors,
        year=2023,
        urls=["https://dl.acm.org/doi/10.1145/example"],
        journal="ACM Transactions on Computer Systems",
        volume="41",
        number="2",
        pages="1-45"
    )
    
    print("Journal Article:")
    print(f"  {article}")
    print(f"  Before CCF: rank={article.rank}, category={article.category}")
    
    # Automatically set CCF attributes based on journal name
    article.set_ccf_attrs_auto()
    
    print(f"  After CCF:  rank={article.rank}, category={article.category}")
    
    # Create a conference paper
    inproc = InProceedings(
        key="conf/sigmod/smith2023",
        title="Efficient Query Processing in Distributed Databases",
        authors=authors[:2],  # Only first two authors
        year=2023,
        urls=["https://dl.acm.org/doi/10.1145/example2"],
        booktitle="SIGMOD",
        pages="123-134"
    )
    
    print(f"\nConference Paper:")
    print(f"  {inproc}")
    print(f"  Before CCF: rank={inproc.rank}, category={inproc.category}")
    
    # Automatically set CCF attributes based on conference name
    inproc.set_ccf_attrs_auto()
    
    print(f"  After CCF:  rank={inproc.rank}, category={inproc.category}")


def example_2_search_with_ccf():
    """Example 2: Simulate search results and add CCF attributes."""
    print("\n=== Example 2: Simulated Search Results with CCF Attributes ===")

    # Simulate some search results (avoiding API issues)
    simulated_results = [
        {
            'title': 'MapReduce: Simplified Data Processing on Large Clusters',
            'authors': ['Jeffrey Dean', 'Sanjay Ghemawat'],
            'venue': 'OSDI',
            'year': 2004,
            'type': 'Conference'
        },
        {
            'title': 'The Google File System',
            'authors': ['Sanjay Ghemawat', 'Howard Gobioff', 'Shun-Tak Leung'],
            'venue': 'SOSP',
            'year': 2003,
            'type': 'Conference'
        },
        {
            'title': 'Bigtable: A Distributed Storage System for Structured Data',
            'authors': ['Fay Chang', 'Jeffrey Dean', 'Sanjay Ghemawat'],
            'venue': 'OSDI',
            'year': 2006,
            'type': 'Conference'
        }
    ]

    print(f"Processing {len(simulated_results)} simulated search results...")

    # Add CCF attributes using the existing function
    results_with_ccf = dblp.add_ccf_attrs(simulated_results, ['class', 'category'])

    for i, result in enumerate(results_with_ccf, 1):
        print(f"\nPaper {i}:")
        print(f"  Title: {result.get('title', 'N/A')}")
        print(f"  Authors: <AUTHORS>
        print(f"  Venue: {result.get('venue', 'N/A')}")
        print(f"  Year: {result.get('year', 'N/A')}")
        print(f"  CCF Rank: {result.get('class', 'Not found')}")
        print(f"  CCF Category: {result.get('category', 'Not found')}")


def example_3_ccf_lookup():
    """Example 3: Direct CCF information lookup."""
    print("\n=== Example 3: Direct CCF Information Lookup ===")
    
    # Test various venue names
    test_venues = [
        "TOCS",  # Journal
        "SIGMOD",  # Conference
        "VLDB",  # Conference
        "Nature",  # Journal (not in CCF)
        "ICSE",  # Conference
        "TPDS",  # Journal
        "NonExistentVenue"  # Should not be found
    ]
    
    print("Looking up CCF information for various venues:")
    
    for venue in test_venues:
        ccf_info = dblp.get_ccf_line(venue)
        print(f"\n  {venue}:")
        
        if ccf_info:
            print(f"    Full Name: {ccf_info.get('name', 'N/A')}")
            print(f"    CCF Class: {ccf_info.get('class', 'N/A')}")
            print(f"    Category: {ccf_info.get('category', 'N/A')}")
            print(f"    Type: {ccf_info.get('type', 'N/A')}")
            print(f"    Publisher: {ccf_info.get('publisher', 'N/A')}")
        else:
            print("    ❌ Not found in CCF catalog")


def example_4_manual_ccf_setting():
    """Example 4: Manual CCF attribute setting with custom venue."""
    print("\n=== Example 4: Manual CCF Setting ===")
    
    # Create a publication with a venue that might not match exactly
    authors = [Author("Research Author")]
    
    article = Article(
        key="journals/example/author2023",
        title="Example Research Paper",
        authors=authors,
        year=2023,
        urls=["https://example.com"],
        journal="ACM Trans. Comput. Syst."  # Abbreviated form
    )
    
    print("Article with abbreviated journal name:")
    print(f"  Journal: {article.journal}")
    print(f"  Before CCF: rank={article.rank}, category={article.category}")
    
    # Try auto setting first
    article.set_ccf_attrs_auto()
    print(f"  After auto CCF: rank={article.rank}, category={article.category}")
    
    # If auto setting didn't work, try manual setting with known venue
    if article.rank is None:
        print("  Auto setting failed, trying manual setting with 'TOCS'...")
        article.set_ccf_attrs("TOCS")
        print(f"  After manual CCF: rank={article.rank}, category={article.category}")


def main():
    """Run all examples."""
    print("CCF Attributes Usage Examples")
    print("=" * 50)
    
    try:
        example_1_manual_creation_with_ccf()
        example_2_search_with_ccf()
        example_3_ccf_lookup()
        example_4_manual_ccf_setting()
        
        print("\n" + "=" * 50)
        print("✅ All examples completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
